<template>
  <div class="vtable-container">
    <!-- 表格操作栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="handleAddColumn">
          添加列
        </el-button>
        <el-button type="success" :icon="Refresh" @click="handleRefreshData" :loading="loading">
          刷新数据
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button :icon="Download" @click="handleExport">
          导出
        </el-button>
      </div>
    </div>

    <!-- VTable 数据表格 -->
    <ListTable :options="tableOptions" :records="tableData" ref="tableRef">
      <ListColumn field="name" title="姓名" width="200"/>
      <ListColumn field="age" title="年龄" width="150"/>
      <ListColumn field="city" title="城市" width="150"/>
      <ListColumn field="gender" title="性别" width="100"/>
      <ListColumn field="comment" title="评论" width="300"/>
    </ListTable>


    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>

    <!-- 列编辑对话框 -->
    <el-dialog
        v-model="columnDialogVisible"
        :title="isEditMode ? '编辑列' : '添加列'"
        width="600px"
        @close="handleDialogClose"
    >
      <el-form
          ref="columnFormRef"
          :model="columnForm"
          :rules="columnFormRules"
          label-width="100px"
      >
        <el-form-item label="列标题" prop="title">
          <el-input v-model="columnForm.title" placeholder="请输入列标题"/>
        </el-form-item>

        <el-form-item label="字段名" prop="field">
          <el-input v-model="columnForm.field" placeholder="请输入字段名"/>
        </el-form-item>

        <el-form-item label="列宽度" prop="width">
          <el-input-number
              v-model="columnForm.width"
              :min="80"
              :max="500"
              placeholder="列宽度"
          />
        </el-form-item>

        <el-form-item label="对齐方式">
          <el-select v-model="textAlign" placeholder="请选择对齐方式">
            <el-option label="左对齐" value="left"/>
            <el-option label="居中" value="center"/>
            <el-option label="右对齐" value="right"/>
          </el-select>
        </el-form-item>

        <el-form-item label="是否排序">
          <el-switch v-model="columnForm.sort"/>
        </el-form-item>

        <el-form-item label="自定义渲染函数">
          <el-input
              v-model="columnForm.formatterCode"
              type="textarea"
              :rows="6"
              placeholder="请输入自定义渲染函数代码，例如：return value + '%'"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="columnDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveColumn">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {computed, onBeforeMount, onUnmounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox, type FormInstance, type FormRules} from 'element-plus'
import {Download, Plus, Refresh} from '@element-plus/icons-vue'
import * as VTable from '@visactor/vtable'
import {ListColumn, ListTable} from '@visactor/vue-vtable'

const tableRef = ref(null)

/**
 * VTable 列接口定义
 */
interface VTableColumn {
  id: string
  field: string
  title: string
  width?: number | 'auto'
  minWidth?: number
  maxWidth?: number
  cellType?: 'text' | 'link' | 'image' | 'video' | 'checkbox' | 'radio' | 'progressbar'
  style?: {
    textAlign?: 'left' | 'center' | 'right'
    fontWeight?: string | number
    fontSize?: number
    color?: string
    bgColor?: string
  }
  sort?: boolean
  customRender?: (args: any) => any
  formatterCode?: string
}

/**
 * 分页接口定义
 */
interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

/**
 * 排序接口定义
 */
interface SortConfig {
  prop: string
  order: 'ascending' | 'descending' | null
}

/**
 * 组件Props接口
 */
interface Props {
  apiUrl?: string
  initialColumns?: VTableColumn[]
  initialData?: any[]
  autoLoad?: boolean
}

/**
 * 组件Emits接口
 */
interface Emits {
  (e: 'selection-change', selection: any[]): void

  (e: 'sort-change', sort: SortConfig): void

  (e: 'data-loaded', data: any[]): void
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  apiUrl: '',
  initialColumns: () => [],
  initialData: () => [],
  autoLoad: true
})

// Emits定义
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref<boolean>(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const columnDialogVisible = ref<boolean>(false)
const isEditMode = ref<boolean>(false)
const editingColumnIndex = ref<number>(-1)
const columnFormRef = ref<FormInstance>()
const vtableInstance = ref<any>(null) // vue-vtable 实例引用
const tableHeight = ref<number>(600)

// 列配置
const columns = ref<VTableColumn[]>([
  {
    id: 'default-1',
    field: 'name',
    title: '名称',
    width: 150,
    style: {textAlign: 'left'},
    sort: true
  },
  {
    id: 'default-2',
    field: 'value',
    title: '数值',
    width: 120,
    style: {textAlign: 'right'},
    sort: true
  },
  {
    id: 'default-3',
    field: 'status',
    title: '状态',
    width: 100,
    style: {textAlign: 'center'},
    sort: false
  }
])

// 分页配置
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 排序配置
const sortConfig = ref<SortConfig>({
  prop: '',
  order: null
})

// 列表单数据
const columnForm = reactive<VTableColumn>({
  id: '',
  field: '',
  title: '',
  width: 150,
  style: {textAlign: 'center'},
  sort: false,
  formatterCode: ''
})

// 列表单验证规则
const columnFormRules: FormRules = {
  title: [
    {required: true, message: '请输入列标题', trigger: 'blur'},
    {min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur'}
  ],
  field: [
    {required: true, message: '请输入字段名', trigger: 'blur'},
    {pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名只能包含字母、数字和下划线，且不能以数字开头', trigger: 'blur'}
  ]
}

// 对齐方式计算属性
const textAlign = computed({
  get: () => columnForm.style?.textAlign || 'center',
  set: (value: 'left' | 'center' | 'right') => {
    if (!columnForm.style) {
      columnForm.style = {}
    }
    columnForm.style.textAlign = value
  }
})

/**
 * 生成唯一ID
 */
const generateId = (): string => {
  return `col_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// VTable 实例的引用，由 ListTable 组件通过 @ready 事件提供
const onTableReady = (args: { vtable: VTable.ListTable }) => {
  vtableInstance.value = args.vtable;
  console.log('VTable instance is ready:', vtableInstance.value);
  // 可以在这里绑定原生 VTable 事件，如果 vue-vtable 的 props 不足以满足需求
  // vtableInstance.value.on('click_cell', handleCellClick);
  // vtableInstance.value.on('sort_click', handleSortClick);
};

// 计算属性，用于 ListTable 的 option prop
const tableOptions = computed(() => ({
  // records: tableData.value, // records 通过 ListTable 的 :records prop 传递
  // columns: columns.value, // columns 通过 ListColumn 组件动态生成
  widthMode: 'standard' as const,
  heightMode: 'autoHeight' as const,
  autoWrapText: true,
  hover: {
    highlightMode: 'row'
  },
  select: {
    highlightMode: 'row'
  },
  theme: VTable.themes.DEFAULT.extends({
    headerStyle: {
      bgColor: '#f5f7fa',
      color: '#606266',
      fontSize: 14,
      fontWeight: 'bold'
    },
    bodyStyle: {
      bgColor: '#ffffff',
      color: '#606266',
      fontSize: 14
    },
    frameStyle: {
      borderColor: '#e4e7ed',
      borderLineWidth: 1
    }
  })
}));

// 辅助函数，用于处理单元格自定义渲染 (如果需要动态组件)
const renderCell = (column: VTableColumn, scope: any) => {
  if (column.customRender) {
    // 注意：直接在模板中使用 Function 创建的组件可能不是最佳实践，
    // 更好的方式是预定义组件或使用更安全的渲染函数。
    // 这里的实现仅为示例，实际应用中需要谨慎处理安全性。
    const renderFunc = column.customRender;
    return {
      render() {
        return renderFunc(scope);
      },
    };
  }
  return scope.value; // 默认返回原始值
};


/**
 * 单元格点击事件 (如果需要通过原生实例监听)
 */
// const handleCellClick = (args: any): void => {
//   console.log('Cell clicked:', args)
// }

/**
 * 排序点击事件 (如果需要通过原生实例监听)
 */
// const handleSortClick = (args: any): void => {
//   console.log('Sort clicked:', args)
//   // 这里可以处理排序逻辑
// }

/**
 * 更新 VTable 数据 - vue-vtable 会自动响应 tableData 的变化
 */
// const updateVTableData = (): void => { ... }

/**
 * 更新 VTable 列配置 - vue-vtable 会自动响应 columns 的变化
 */
// const updateVTableColumns = (): void => { ... }


/**
 * 异步加载数据
 */
const loadData = async (): Promise<void> => {
  if (!props.apiUrl && props.initialData.length === 0) {
    // 模拟数据
    tableData.value = [
      {id: 1, name: '项目A', value: 1000, status: '进行中'},
      {id: 2, name: '项目B', value: 2000, status: '已完成'},
      {id: 3, name: '项目C', value: 1500, status: '暂停'},
      {id: 4, name: '项目D', value: 3000, status: '进行中'},
      {id: 5, name: '项目E', value: 2500, status: '已完成'}
    ]
    pagination.total = tableData.value.length
    // updateVTableData() // vue-vtable 会自动响应 tableData 的变化
    emit('data-loaded', tableData.value)
    return
  }

  if (props.initialData.length > 0) {
    tableData.value = props.initialData
    pagination.total = tableData.value.length
    // updateVTableData() // vue-vtable 会自动响应 tableData 的变化
    emit('data-loaded', tableData.value)
    return
  }

  loading.value = true
  try {
    // 构建请求参数
    const params = {
      page: pagination.currentPage,
      size: pagination.pageSize,
      ...(sortConfig.value.prop && {
        sortBy: sortConfig.value.prop,
        sortOrder: sortConfig.value.order
      })
    }

    // 这里应该调用实际的API
    // const response = await request.get(props.apiUrl, { params })
    // tableData.value = response.data.data
    // pagination.total = response.data.total

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成示例数据
    const sampleData = Array.from({length: 20}, (_, i) => ({
      name: `示例项目 ${i + 1}`,
      value: Math.floor(Math.random() * 1000) + 100,
      status: ['进行中', '已完成', '待开始'][Math.floor(Math.random() * 3)],
      date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      category: ['类别A', '类别B', '类别C'][Math.floor(Math.random() * 3)]
    }))

    tableData.value = sampleData
    pagination.total = sampleData.length

    // updateVTableData() // vue-vtable 会自动响应 tableData 的变化
    emit('data-loaded', tableData.value)
    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('数据加载失败:', error)
    ElMessage.error('数据加载失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新数据
 */
const handleRefreshData = async (): Promise<void> => {
  await loadData()
}

/**
 * 添加列
 */
const handleAddColumn = (): void => {
  isEditMode.value = false
  editingColumnIndex.value = -1

  // 重置表单
  Object.assign(columnForm, {
    id: generateId(),
    field: '',
    title: '',
    width: 150,
    style: {textAlign: 'center'},
    sort: false,
    formatterCode: ''
  })

  columnDialogVisible.value = true
}

/**
 * 编辑列
 */
const handleEditColumn = (index: number): void => {
  isEditMode.value = true
  editingColumnIndex.value = index

  const column = columns.value[index]
  Object.assign(columnForm, {
    ...column,
    formatterCode: column.formatterCode || ''
  })

  columnDialogVisible.value = true
}

/**
 * 删除列
 */
const handleDeleteColumn = async (index: number): Promise<void> => {
  try {
    await ElMessageBox.confirm(
        '确定要删除这一列吗？',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    columns.value.splice(index, 1)
    // updateVTableColumns() // vue-vtable 会自动响应 columns 的变化
    ElMessage.success('列删除成功')
  } catch {
    // 用户取消删除
  }
}

/**
 * 保存列配置
 */
const handleSaveColumn = async (): Promise<void> => {
  if (!columnFormRef.value) return

  try {
    await columnFormRef.value.validate()

    // 创建自定义渲染函数
    let customRender: ((args: any) => any) | undefined

    if (columnForm.formatterCode.trim()) {
      try {
        // 创建安全的自定义渲染函数
        customRender = new Function('args', `
          try {
            const { value, record, row, col } = args
            ${columnForm.formatterCode}
          } catch (error) {
            console.error('自定义渲染函数执行错误:', error)
            return value
          }
        `) as any
      } catch (error) {
        ElMessage.error('自定义渲染函数语法错误，请检查代码')
        return
      }
    }

    const newColumn: VTableColumn = {
      id: columnForm.id || generateId(),
      field: columnForm.field,
      title: columnForm.title,
      width: columnForm.width,
      style: columnForm.style || {textAlign: 'center'},
      sort: columnForm.sort,
      customRender,
      formatterCode: columnForm.formatterCode
    }

    if (isEditMode.value && editingColumnIndex.value >= 0) {
      // 编辑模式
      columns.value[editingColumnIndex.value] = newColumn
      ElMessage.success('列更新成功')
    } else {
      // 添加模式
      columns.value.push(newColumn)
      ElMessage.success('列添加成功')
    }

    // 更新 VTable 列配置 - vue-vtable 会自动响应 columns 的变化
    // updateVTableColumns()
    columnDialogVisible.value = false
  } catch {
    // 表单验证失败
  }
}

/**
 * 对话框关闭处理
 */
const handleDialogClose = (): void => {
  columnFormRef.value?.resetFields()
}


/**
 * 分页大小变化
 */
const handleSizeChange = (size: number): void => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

/**
 * 当前页变化
 */
const handleCurrentChange = (page: number): void => {
  pagination.currentPage = page
  loadData()
}

/**
 * 导出数据
 */
const handleExport = (): void => {
  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  // 这里可以实现导出逻辑
  ElMessage.info('导出功能开发中...')
}

// 初始化
onBeforeMount(async () => {
  // 如果有初始列配置，使用它们
  if (props.initialColumns.length > 0) {
    columns.value = props.initialColumns
  }

  // 自动加载数据
  if (props.autoLoad) {
    await loadData()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  // 使用 @visactor/vue-vtable 时，组件会自动处理实例的销毁
  // 如果通过 onTableReady 获取了原生实例并手动绑定了事件，
  // 可以在这里解绑：
  // if (vtableInstance.value && vtableInstance.value.off) {
  //   vtableInstance.value.off('click_cell', handleCellClick);
  //   vtableInstance.value.off('sort_click', handleSortClick);
  // }
  // vtableInstance.value = null;
})

// 暴露方法给父组件
defineExpose({
  loadData,
  getSelectedRows: () => selectedRows.value,
  getTableData: () => tableData.value,
  addColumn: handleAddColumn,
  refreshData: handleRefreshData
})
</script>

<style scoped>
.vtable-container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 8px;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.column-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.column-header:hover .column-actions {
  opacity: 1;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.vtable-wrapper {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

:deep(.el-pagination) {
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .column-actions {
    opacity: 1;
  }
}
</style>