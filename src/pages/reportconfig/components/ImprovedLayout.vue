<template>
  <el-container>
    <el-header>
      <el-row class="report-header">
        <el-col :span="8">
          <h1 v-if="!isEditingTitle" @click="startTitleEdit">
            {{ ruleForm.name || '点击编辑报表标题' }}
            <el-icon class="edit-icon">
              <Edit/>
            </el-icon>
          </h1>
          <el-input
              v-else
              v-model="ruleForm.name"
              @blur="saveTitleEdit"
              @keyup.enter="saveTitleEdit"
              class="title-input"
              size="large"
          />
        </el-col>
        <el-col :span="8" :offset="6">
          <div style="margin-top: 10px">
            <a href="#" v-if="!isEditingUrl" @click="startUrlEdit" style="font-size: 24px;color: black;margin-top: 5px">
              {{ ruleForm.code || '点击编辑URL地址' }}
              <el-icon>
                <Edit/>
              </el-icon>
            </a>
            <el-input
                v-else
                v-model="ruleForm.code"
                @blur="saveUrlEdit"
                @keyup.enter="saveUrlEdit"
                class="url-input"
                size="large"
                placeholder="请输入URL地址"
            />
          </div>
        </el-col>
      </el-row>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main>

      <div class="search-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon>
              <Search/>
            </el-icon>
            <span>搜索条件</span>
          </div>
          <div class="header-right">
            <el-button type="primary" size="large" icon="Plus" @click="handleAddField">
              添加条件
            </el-button>
            <el-button size="large" text @click="toggleSearchCard">
              {{ searchCardExpanded ? '收起' : '展开' }}
            </el-button>
          </div>
        </div>

        <div class="card-content" v-show="searchCardExpanded">
          <el-form label-width="120px" class="conditions-form">
            <el-row :gutter="24">
              <template v-for="(condition, name, index) in searchConditionsConfig" :key="index">
                <el-col :span="8" v-if="condition">
                  <el-form-item :label="condition.label || name" class="search-form-item">
                    <div class="form-item-wrapper">
                      <!-- 不同类型的表单控件 -->
                      <template
                          v-if="condition.type === 'select' || condition.type === 'select-http' || condition.type === 'select-https'">
                        <el-select
                            v-model="searchValues[name]"
                            filterable
                            :multiple="condition.multiple === true"
                            placeholder="请选择下拉值"
                            class="search-input"
                            @change="handleSearch">
                          <el-option
                              v-for="option in (condition.optionsData || [])"
                              :key="option?.value || option"
                              :label="option?.label || option"
                              :value="option?.value || option">
                          </el-option>
                        </el-select>
                      </template>
                      <template v-else-if="condition.type === 'date'">
                        <el-date-picker
                            v-model="searchValues[name]"
                            type="date"
                            :placeholder="name"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="handleSearch"
                            class="search-input"
                        ></el-date-picker>
                      </template>
                      <template v-else>
                        <el-input
                            v-model="searchValues[name]"
                            :placeholder="name"
                            clearable
                            @input="handleSearch"
                            class="search-input"
                        ></el-input>
                      </template>

                      <!-- 编辑和删除图标 -->
                      <div class="field-actions">
                        <el-button-group>
                          <el-button link :icon="Edit" @click="handleEditField(name, condition)"/>
                          <el-button link :icon="Delete" @click="handleDeleteField(name)"/>
                        </el-button-group>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
            <div class="search-buttons">
              <div v-if="searchError" class="search-error">{{ searchError }}</div>
              <div class="search-actions">
                <el-button type="success" size="large" @click="handleSearch" :loading="isSearching" icon="Search"
                           class="action-btn primary-action">
                  {{ isSearching ? '搜索中...' : '立即搜索' }}
                </el-button>
                <el-button size="large" @click="resetSearch" icon="Refresh" class="action-btn secondary-action">
                  重置条件
                </el-button>
              </div>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 数据展示标签页区域 -->
      <div class="data-tabs-section">
        <el-tabs v-model="activeTab" class="data-tabs" type="border-card">
          <el-tab-pane name="datasource">
            <template #label>
                <span class="tab-label">
                  <el-icon><DataLine/></el-icon>
                  数据源配置
                </span>
            </template>
            <el-row>
              <el-col :span="16">
                <div class="config-section">
                  <div class="config-content">
                    <!-- 数据源配置 -->
                    <div class="config-item datasource-config">
                      <label class="config-label">
                        <el-icon>
                          <DataLine/>
                        </el-icon>
                        数据源
                      </label>
                      <el-select
                          v-model="ruleForm.db"
                          placeholder="请选择数据源"
                          class="datasource-select"
                          size="large"
                          filterable
                      >
                        <el-option
                            v-for="option in dbOptions"
                            :key="option.value || option"
                            :label="option.label || option"
                            :value="option.value || option"
                        />
                      </el-select>
                    </div>

                    <!-- SQL配置 -->
                    <div class="config-item sql-config">
                      <label class="config-label">
                        <el-icon>
                          <EditPen/>
                        </el-icon>
                        SQL编辑
                      </label>
                      <div class="sql-config-wrapper">
                        <SqlConfigPanel
                            ref="sqlConfigPanelRef"
                            :model-value="ruleForm"
                            @apply="handleSqlConfigApply"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 数据展示标签页 -->
          <el-tab-pane name="datatable">
            <template #label>
                <span class="tab-label">
                  <el-icon><Grid/></el-icon>
                  数据展示
                </span>
            </template>
            <div style="display: flex;justify-content: flex-end; gap: 10px; ">
              <el-button type="primary" size="large" icon="Plus" @click="handleAddColumn">
                添加列
              </el-button>
              <el-dropdown>
                <el-button size="large" icon="Download">导出</el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>导出Excel</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <el-table
                :data="tableProp"
                :summary-method="getSum"
                show-summary>
              <!-- 修改表格列定义，为每个列标题添加编辑图标 -->
              <template v-for="(column, index) in columnsConfig" :key="column?.prop || index">
                <el-table-column
                    v-if="column"
                    :prop="column?.label"
                    :label="column?.label"
                    :sortable="column?.sort"
                    :align="column?.align || 'center'">
                  <template #header>
                    <div style="display: flex; align-items: center; column-gap: 4px">
                      <span>{{ column?.label || '' }}</span>
                      <el-button-group>
                        <el-button link :icon="Edit" @click="handleEditColumn(index)"/>
                        <el-button link :icon="Delete" @click="handleDeleteColumn(index)"/>
                      </el-button-group>
                    </div>
                  </template>
                  <template #default="scope">
                    <!-- 使用v-html渲染可能包含HTML的内容，同时使用:style应用样式 -->
                    <span
                        v-html="asyncFormatter(scope.row, column, scope.row[column?.label], scope.$index)"
                        :style="scope.row.style && scope.row.style[column?.label] ? {color: scope.row.style[column?.label]} : {}"
                    ></span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />

          </el-tab-pane>
        </el-tabs>
      </div>
    </el-main>
    <el-divider></el-divider>
    <el-footer>
      <el-row>
        <el-button-group style="display: flex; align-items: center; column-gap: 20px">
          <el-button type="primary" icon="Document" @click="saveReport">保存</el-button>
          <el-button type="success" icon="View" @click="refreshPreview">预览</el-button>
        </el-button-group>
      </el-row>
    </el-footer>

  </el-container>

  <!-- 字段编辑抽屉 -->
  <el-drawer
      v-model="drawerVisible"
      title="编辑搜索条件"
      direction="rtl"
      size="40%"
      :before-close="handleDrawerClose"
      destroy-on-close
  >
    <div class="drawer-content">
      <FieldEditForm
          v-if="drawerVisible"
          :modelValue="editingField"
          @save="handleFieldSave"
          @cancel="handleFieldCancel"
      />
    </div>
  </el-drawer>

  <!-- 列配置 Drawer -->
  <el-drawer
      v-model="columnDrawerVisible"
      :title="editingColumn ? '编辑列配置' : '新增列配置'"
      direction="rtl"
      size="60%"
      :before-close="handleColumnDrawerClose"
      destroy-on-close
  >
    <div class="column-drawer-content">
      <ColumnEditForm
          v-if="columnDrawerVisible"
          :modelValue="editingColumn"
          :isNew="!editingColumn"
          :selectedFields="fields"
          :existingColumns="columnsConfig"
          @save="handleColumnSave"
          @cancel="handleColumnCancel"
      />
    </div>
  </el-drawer>

</template>

<script setup lang="ts" name="ImprovedLayout">
import {computed, onBeforeMount, onMounted, reactive, ref} from 'vue'
import {DataLine, Delete, Edit, EditPen, Grid, Search} from '@element-plus/icons-vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import SqlConfigPanel from './SqlConfigPanel.vue'
import ColumnEditForm from './ColumnEditForm.vue'
import FieldEditForm from './FieldEditForm.vue'
import {reportApi} from '../services/api'
import {convertToTableColumns, processTableData} from '../utils/reportUtils'
import {FieldConfig, ReportType} from "../types";
import {Parser, Select} from "node-sql-parser";
import {ColumnConfig, DiyReportForm, Option, SearchConditionsConfig as SCC, SearchValues as SV} from "../types.ts";

const parser = new Parser();

// 响应式数据
const isEditingTitle = ref(false)
const isEditingUrl = ref(false)
const searchCardExpanded = ref(true)
const activeTab = ref('datasource')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(10)

// 表单数据
const ruleForm = ref<DiyReportForm>({
  name: '自定义报表',
  code: '',
  db: '',
  sql: 'select date,type from a;',
  desc: '',
  type: ReportType.DIY
})

// 数据库选项
const dbOptions = ref<Option[]>([])

// 搜索相关
const searchValues = reactive<SV>({})
const searchConditionsConfig = ref<SCC>()
const searchError = ref('')
const isSearching = ref(false)

// 列配置
const columnsConfig = ref<ColumnConfig[]>([]);
const fields = ref<{ column: any }[]>([{column: ''}])
const fieldsConfig = ref<FieldConfig[]>([
  {
    id: "field_1",
    label: "开始日期",
    name: "startDate",
    attribute: "date",
    type: "date",
    operator: ">=",
    multiple: false,
    dataEndpoint: '',
    optionsData: [],
    value: '30 days ago'
  },
  {
    id: "field_2",
    label: "结束日期",
    name: "endDate",
    attribute: "date",
    type: "date",
    operator: "<",
    multiple: false,
    dataEndpoint: '',
    optionsData: [],
    value: 'today'
  }
]);

// 表格数据
const tableProp = ref()
const columnsProp = ref()

const drawerVisible = ref(false)
const columnDrawerVisible = ref(false)

// 编辑状态

const editingField = ref<FieldConfig>({
  id: '',
  label: '',
  name: '',
  attribute: '',
  type: '',
  operator: ''
})
const editingFieldIndex = ref(-1)
const editingColumn = ref({})
const editingColumnIndex = ref(-1)

// 组件引用
const sqlConfigPanelRef = ref(null)


const startTitleEdit = () => {
  isEditingTitle.value = true
}

const saveTitleEdit = () => {
  isEditingTitle.value = false
}

const startUrlEdit = () => {
  isEditingUrl.value = true
}

const saveUrlEdit = () => {
  isEditingUrl.value = false
}

const toggleSearchCard = () => {
  searchCardExpanded.value = !searchCardExpanded.value
}

// SQL配置相关
const handleSqlConfigApply = (sql: string) => {
  if (ruleForm.value.db === '' || ruleForm.value.db === null) {
    ElMessage.error('数据源未选择!')
    return;
  }
  ruleForm.value.sql = sql
  handleSql()
  refreshPreview()
  activeTab.value = 'datatable'
}
// 搜索条件相关
const handleAddField = () => {
  editingField.value = {
    id: `field_${Date.now()}`,
    name: 'xxx',
    label: '新字段',
    type: 'text',
    operator: 'eq',
    value: '',
    attribute: '',
    multiple: false,
    optionsData: [],
    url: '',
    valueField: 'value',
    labelField: 'label',
    method: 'GET'
  }
  editingFieldIndex.value = -1  // -1 表示新增
  drawerVisible.value = true
}

const handleEditField = (name: string, condition: any) => {
  // 从fieldsConfig中查找对应的字段配置
  const fieldIndex = fieldsConfig.value.findIndex(field => field.name === name);
  if (fieldIndex >= 0) {
    editingField.value = {...fieldsConfig.value[fieldIndex]};
    editingFieldIndex.value = fieldIndex;
  } else {
    // 如果在fieldsConfig中找不到，使用传入的condition
    editingField.value = {
      name: name,
      ...(condition || {})
    };
    editingFieldIndex.value = -1;
  }
  drawerVisible.value = true;
}

const handleDeleteField = async (name: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个搜索条件吗？', '确认删除', {
      type: 'warning'
    });

    // 从fieldsConfig中删除
    const fieldIndex = fieldsConfig.value.findIndex(field => field.name === name);
    if (fieldIndex >= 0) {
      fieldsConfig.value.splice(fieldIndex, 1);
    }

    // 从searchConditionsConfig和searchValues中删除
    delete searchConditionsConfig.value[name];
    delete searchValues[name];

    ElMessage.success('搜索条件已删除');
  } catch {
    // 用户取消删除
  }
}

const handleFieldSave = async (fieldData: any) => {
  const updatedField = fieldData;
  if (editingFieldIndex.value === -1) {
    // 新增字段
    if (!updatedField.name) {
      ElMessage.warning('字段名称不能为空');
      return;
    }

    // 检查字段名是否重复
    const existingField = fieldsConfig.value.find(field => field.name === updatedField.name);
    if (existingField) {
      ElMessage.warning('字段名称已存在，请使用其他名称');
      return;
    }

    // 添加到字段配置
    fieldsConfig.value.push({...updatedField});
    ElMessage.success('字段添加成功');
  } else {
    // 编辑现有字段
    fieldsConfig.value[editingFieldIndex.value] = {...updatedField};
    ElMessage.success('字段配置已更新');
  }

  // 重新构建搜索条件配置
  const conditions: { [key: string]: any } = {};
  fieldsConfig.value.forEach(field => {
    if (field && field.name) {
      conditions[field.name] = {
        label: field.label,
        name: field.name,
        type: field.type || 'text',
        value: field.value || '',
        operator: field.operator || 'eq',
        attribute: field.attribute || '',
        multiple: field.multiple || false,
        optionsData: field.optionsData || []
      };
      if (field.value !== undefined && field.value !== null && field.value !== '') {
        // 同步更新searchValues中的默认值
        // 根据条件类型和默认值更新searchValues
        if (field.type === 'date') {
          searchValues[field.name] = parseDateExpression(field.value);
        } else if (field.type === 'select' && field.multiple) {
          searchValues[field.name] = Array.isArray(field.value) ? [...field.value] : [field.value];
        } else {
          searchValues[field.name] = field.value;
        }
      }
    }
  });

  // 清空并更新搜索条件配置
  searchConditionsConfig.value = conditions;

  drawerVisible.value = false;
  editingField.value = {
    id: '',
    label: '',
    name: '',
    attribute: '',
    type: '',
    operator: ''
  }

  await refreshPreview();
}

const handleFieldCancel = () => {
  drawerVisible.value = false
  editingField.value = {
    id: '',
    label: '',
    name: '',
    attribute: '',
    type: '',
    operator: ''
  }
  editingFieldIndex.value = -1
}

// 列配置相关
const handleAddColumn = () => {
  editingColumn.value = {}
  editingColumnIndex.value = -1
  columnDrawerVisible.value = true
}

const handleEditColumn = (index: number) => {
  editingColumn.value = {...columnsConfig.value[index]}
  editingColumnIndex.value = index
  columnDrawerVisible.value = true
}

const handleDeleteColumn = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个列吗？', '确认删除', {
      type: 'warning'
    })
    columnsConfig.value.splice(index, 1)
    ElMessage.success('列已删除')
  } catch {
    // 用户取消删除
  }
}

const handleColumnSave = async (columnData: any) => {
  if (editingColumnIndex.value >= 0) {
    columnsConfig.value[editingColumnIndex.value] = columnData
  } else {
    columnsConfig.value.push(columnData)
  }
  columnDrawerVisible.value = false
  ElMessage.success('列配置已保存')
  // 刷新预览
  await refreshPreview()
}

const handleColumnCancel = () => {
  columnDrawerVisible.value = false
  editingColumn.value = {
    id: '',
    label: '',
    name: '',
    attribute: '',
    type: '',
    operator: ''
  }
  editingColumnIndex.value = -1
}


// 新增一个computed属性来处理选中字段生成的JS数组字符串
const selectedFieldsArray = computed(() => {
  const selectedFields = fields.value?.map(column => {

    return `{ value: '${column.column}'}`;
  });
  return `[${selectedFields.join(', ')}]`;
});

// 添加列配置
function addColumnConfig(column: string) {
  console.log(column)
  if (!column) return;

  // 检查是否已存在相同的列
  const existingColumn = columnsConfig.value.find(c => c.label === column);
  if (existingColumn) {
    console.log(`Column ${column} already exists, skipping`);
    return;
  }

  const columnFormat = `// 你可以使用selectedFields变量来访问当前查询的字段\n//const selectedFields = ${selectedFieldsArray.value};\n
  // const format = (row, column) => {
  return row.${column};
  // }`;

  columnsConfig.value.push({
    label: column,
    format: columnFormat,
    align: 'left',
    color: '#000000',
    handler: 'text',
    total: false,
    sort: false,
    editorHeight: 300
  });
}

// 搜索相关
const handleSearch = async () => {
  try {
    isSearching.value = true
    searchError.value = ''

    // 检查是否有搜索条件
    const hasConditions = Object.keys(searchValues).some(key => {
      const value = searchValues[key]
      return value !== undefined && value !== null && value !== ''
    })

    if (!hasConditions) {
      // 如果没有搜索条件，则刷新预览
      await refreshPreview()
      return
    }

    // 构建搜索条件
    // 更新搜索条件配置
    searchConditionsConfig.value = fieldsConfig.value.reduce((acc, curr) => {
      if (curr.name) {
        const displayKey = curr.name
        const apiKey = curr.name

        acc[displayKey] = {
          value: curr.value !== undefined && curr.value !== null ? curr.value : '',
          type: curr.type || 'text',
          multiple: curr.multiple || false,
          optionsData: curr.optionsData || [],
          dataEndpoint: curr.dataEndpoint || '',
          attribute: curr.attribute || '',
          operator: curr.operator || 'eq',
          name: curr.name || '',
          label: curr.label || '',
          apiField: apiKey
        }
      }
      return acc
    }, {})

    // 创建搜索请求数据
    const data = {
      ...ruleForm,
      searchConditions: searchConditionsConfig.value,
      searchValues: searchValues,
      condition: fieldsConfig.value,
      columns: columnsConfig.value,
    }

    console.log('搜索请求数据:', data)
    const searchData = await reportApi.previewReport(data)
    console.log('搜索响应数据:', searchData)

    if (searchData) {
      // 处理搜索结果
      tableProp.value = processTableData(searchData, columnsConfig.value)
      console.log('处理后的搜索数据:', tableProp.value)

      // 更新总数
      total.value = Array.isArray(searchData) ? searchData.length : 0;

      ElMessage.success('搜索完成')
    } else {
      ElMessage.warning('未找到匹配的数据')
    }
  } catch (error) {
    console.log('搜索处理错误:', error)
    ElMessage.error('搜索失败')
  } finally {
    isSearching.value = false
  }
}

const resetSearch = async () => {
  Object.keys(searchValues).forEach(key => {
    searchValues[key] = ''
  })
  ElMessage.info('搜索条件已重置')
  // 重置后刷新预览
  await refreshPreview()
}

// 格式化函数
const asyncFormatter = (row, column, cellValue, index) => {
  // 首先处理空值情况
  if (!row.format) return cellValue;

  let result = cellValue;

  // 应用自定义格式化
  if (typeof row.format[column.label] === 'string') {
    try {
      result = implement(row, column, cellValue, index);
    } catch (error) {
      console.error("Error executing custom formatter function:", error);
    }
  }

  // 处理处理器
  if (row.handler && typeof row.handler[column.label] === 'string') {
    const handlerType = row.handler[column.label];

    // 处理链接类型
    if (handlerType === 'link') {
      // 如果单元格值是一个URL
      if (typeof result === 'string' && (
          result.startsWith('http://') ||
          result.startsWith('https://') ||
          result.startsWith('//') ||
          result.startsWith('/')
      )) {
        return createLink(result, result);
      }
    } else if (handlerType === 'formatCurrency') {
      return formatCurrency(result);
    }
  }

  return result;
};

const implement = (row, column, cellValue, index) => {
  if (!row.format || !row.format[column.label]) {
    return cellValue;
  }

  try {
    const func = new Function('row', 'cellValue', row.format[column.label]);
    return func(row, cellValue);
  } catch (error) {
    console.error("Error executing custom formatter function:", error);
    return cellValue;
  }
};

const formatCurrency = (amount) => {
  const number = Number(amount);
  if (isNaN(number)) {
    return amount;
  }

  // 使用Intl.NumberFormat来格式化数字为带千位分隔符的字符串
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(number / 100);
};


// 创建链接HTML
const createLink = (text, url, options = {}) => {
  if (!text || !url) return text || '';

  const target = options.target || '_blank';
  let className = 'report-link';

  return `<a href="${url}" target="${target}" class="${className}">${text}</a>`;
};

const getSum = (param: any) => {
  const {columns, data} = param;
  const sums = [];

  columns.forEach((column, index) => {
    // 确保使用正确的属性名
    const propName = column.property || column.prop;

    // 检查是否应该计算该列的合计
    if (column.total === false) {
      sums[index] = '';
      return;
    }

    const values = data.map(item => {
      if (item.total && item.total[propName] === false) {
        return undefined;
      }

      let rawValue = item[propName];

      if (item.format && item.format[propName]) {
        try {
          // 使用 implement 函数获取格式化后的值
          const formattedValue = implement(item, {prop: propName}, rawValue, index);

          // 如果返回的是数字，直接使用
          if (typeof formattedValue === 'number') {
            rawValue = formattedValue;
          }
          // 如果是字符串，尝试提取数字部分
          else if (typeof formattedValue === 'string') {
            // 移除非数字字符（保留小数点和负号）
            const cleanedValue = formattedValue.replace(/[^\d.-]/g, '');
            const parsedValue = parseFloat(cleanedValue);
            if (!isNaN(parsedValue)) {
              rawValue = parsedValue;
            }
          }
        } catch (error) {
          console.error('Error applying formatter in sum calculation:', error);
        }
      }

      // 尝试将值转换为数字
      const numberValue = typeof rawValue === 'number' ? rawValue : Number(rawValue);
      return !isNaN(numberValue) ? numberValue : undefined;
    }).filter((value:any) => value !== undefined);

    // 计算合计
    if (values.length > 0) {
      try {
        sums[index] = values.reduce((prev :number, curr:number) => prev + curr, 0);
        const isCurrencyColumn = data[index].handler[column.property] === 'formatCurrency';
        // 只有当列是货币类型时才应用货币格式化
        if (isCurrencyColumn) {
          sums[index] = formatCurrency(sums[index]);
        }
      } catch (error) {
        console.error('Error calculating sum:', error);
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });

  return sums;
};

// 分页相关
const handleSizeChange = (size) => {
  pageSize.value = size
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 顶部按钮相关
const saveReport = () => {
  ElMessage.success('报表已保存')
}

const refreshPreview = async () => {
  try {
    ElMessage.info('正在刷新预览...')

    // 构建搜索条件和字段映射
    // 将搜索条件配置转换为 DirectPreview 组件需要的格式
    // 更新 searchConditionsConfig
    searchConditionsConfig.value = fieldsConfig.value.reduce((acc, curr) => {
      // 使用label属性作为键名，如果没有label属性，则使用field属性
      if (curr.name) {
        const displayKey = curr.name; // 显示用的键名（如"资方"）
        const apiKey = curr.name

        // 包含完整的条件信息，而不仅仅是值
        acc[displayKey] = {
          value: curr.value !== undefined && curr.value !== null ? curr.value : '',
          type: curr.type || 'text',
          multiple: curr.multiple || false,
          optionsData: curr.optionsData || [],
          dataEndpoint: curr.dataEndpoint || '',
          attribute: curr.attribute || '',
          operator: curr.operator || 'eq',
          name: curr.name || '',
          label: curr.label || '',
          apiField: apiKey
        };
      }
      return acc;
    }, {});

    // 创建包含搜索条件配置和搜索值的数据，完全分开
    const data = {
      ...ruleForm,
      searchConditions: searchConditionsConfig.value, // 搜索条件配置
      searchValues: searchValues, // 搜索值
      // 不再将搜索条件值合并到 condition 数组中
      condition: fieldsConfig.value, // 只包含搜索条件配置
      columns: columnsConfig.value,
    };

    console.log('预览请求数据:', data);
    const previewData = await reportApi.previewReport(data);
    console.log('预览响应数据:', previewData);

    if (previewData) {
      // 转换列配置为表格列属性
      columnsProp.value = convertToTableColumns(columnsConfig.value);
      // 处理表格数据
      tableProp.value = processTableData(previewData, columnsConfig.value);
      // 更新总数
      total.value = Array.isArray(previewData) ? previewData.length : 0;

      ElMessage.success('预览数据已更新');
    } else {
      ElMessage.warning('无法获取预览数据');
    }
  } catch (error) {
    ElMessage.error('预览刷新失败');
  }
}


const handleDrawerClose = (done: any) => {
  done()
}

const handleColumnDrawerClose = (done: any) => {
  done()
}


// 日期表达式解析函数
const parseDateExpression = (expression:string) => {
  if (!expression ) return '';

  // 格式化日期的辅助函数
  const formatDate = (date:any) => {
    return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0');
  };

  if (expression === 'today') {
    return formatDate(new Date());
  } else if (expression.endsWith('days ago')) {
    const days = parseInt(expression.split(' ')[0]);
    const date = new Date();
    date.setDate(date.getDate() - days);
    return formatDate(date);
  } else if (expression.endsWith('month ago')) {
    const months = parseInt(expression.split(' ')[0]);
    const date = new Date();
    date.setMonth(date.getMonth() - months);
    return formatDate(date);
  }
  return expression;
};

// 初始化数据
const initData = () => {
  try {
    // 从fieldsConfig构建searchConditionsConfig
    const conditions: { [key: string]: any } = {}
    fieldsConfig.value.map(field => {
      if (field && field.name) {
        conditions[field.name] = {
          label: field.label,
          name: field.name,
          id: field.id,
          type: field.type,
          multiple: field.multiple,
          optionsData: field.optionsData,
          attribute: field.attribute,
          operator: field.operator,
          dataEndpoint: field.dataEndpoint,
          value: field.value,
        }
      }
    })
    searchConditionsConfig.value = conditions

    for (const key in searchValues) {
      if (!searchConditionsConfig.value[key]) {
        delete searchValues[key];
      }
    }

    // 从搜索条件配置中初始化搜索值
    for (const key in searchConditionsConfig.value) {
      const condition = searchConditionsConfig.value[key];
      if (condition) {
        // 只对不存在的字段进行初始化，保留用户已选择的值
        if (searchValues[key] === undefined) {
          // 根据条件类型和默认值初始化
          if (condition.value !== undefined && condition.value !== null && condition.value !== '') {
            // 有配置默认值
            if (condition.type === 'date') {
              searchValues[key] = parseDateExpression(condition.value);
            } else if (condition.type === 'select' && condition.multiple) {
              searchValues[key] = Array.isArray(condition.value) ? [...condition.value] : [condition.value];
            } else {
              searchValues[key] = condition.value;
            }
          } else {
            console.log(condition)
            // 没有配置默认值，使用类型默认值
            if (condition.type === 'select' && condition.multiple) {
              searchValues[key] = [];
            } else if (condition.type === 'date') {
              searchValues[key] = '';
            } else {
              searchValues[key] = '';
            }
          }
        }
      }
    }

    console.log('searchValues', searchValues);
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

const handleSql = (() => {
  if (ruleForm.value.sql && ruleForm.value.sql.length > 0) {
    try {
      columnsConfig.value = []
      const astObject = parser.parse(ruleForm.value.sql);
      const ast = (Array.isArray(astObject.ast) ? astObject.ast[0] : astObject.ast) as Select
      const columns = ast.columns
      fields.value = columns.map(column => {
        if (column.as) {
          return {column: column.as};
        } else if (column.expr && column.expr.hasOwnProperty('column') && column.expr.column) {
          return {column: column.expr.column};
        }
        return {column: ''};
      }).filter(item => item !== null); // 过滤掉null值
      // 根据提取的字段自动生成列配置
      fields.value.map(column => {
        if (column && column.column) {
          addColumnConfig(column.column);
        }
      });
    } catch (err) {
      ElMessage.warning('解析SQL失败,请手动添加列!');
      fields.value = [{column: ''}];
    }
  }
});

onMounted(() => {
  initData()
})

// 生命周期
onBeforeMount(async () => {
  dbOptions.value = await reportApi.getDatabases()
})
</script>

<style lang="scss" scoped>
@import '../styles/improved-layout.scss';
</style>