<template>
  <div class="description-config-panel">
    <div class="editor-container">
      <MonacoEditor
          v-model:value="description"
          language="markdown"
          :theme="theme"
          :options="editorOptions"
          :on-editor-will-mount="(monaco) => {
            return {
              lineNumbers: 'on',
              folding: true,
              glyphMargin: true,
            }
          }"
          :on-editor-did-mount="updateMonacoEditor"
      />
    </div>
    <div class="panel-actions">
      <el-button @click="$emit('close')" icon="Close">取消</el-button>
      <el-button type="primary" @click="applyChanges" icon="Check">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type {
  Monaco,
  MonacoCodeEditorTheme,
  MonacoEditorOptions
} from 'monaco-editor-component/vue';
import { MonacoEditor } from 'monaco-editor-component/vue';
import type { MonacoCodeEditor } from 'monaco-editor-component';
import { ElMessage } from 'element-plus';
import '../useWorker.ts'

// 定义props和emit
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'close', 'apply']);

// 本地描述数据
const description = ref(props.modelValue);

// 监听props变化，更新本地描述
watch(() => props.modelValue, (newVal) => {
  description.value = newVal;
});

// 编辑器配置
const theme = ref<MonacoCodeEditorTheme>('vs-dark');
const editorOptions: MonacoEditorOptions = {
  tabCompletion: 'on',
  cursorSmoothCaretAnimation: 'on',
  formatOnPaste: true,
  mouseWheelZoom: true,
  folding: true,
  autoClosingBrackets: 'always',
  autoClosingOvertype: 'always',
  autoClosingQuotes: 'always',
  automaticLayout: true,
  minimap: {
    enabled: false
  }
};

// 编辑器实例
const monacoEditor = ref<MonacoCodeEditor | null>(null);

// 更新编辑器实例
const updateMonacoEditor = (editor: MonacoCodeEditor) => {
  monacoEditor.value = editor;
};

// 应用更改
const applyChanges = () => {
  emit('update:modelValue', description.value);
  emit('apply', description.value);
  ElMessage.success('描述已更新');
  emit('close');
};
</script>

<style scoped>
.description-config-panel {
  height: 100%;
  overflow: auto;
  padding: 15px;
}

.editor-container {
  height: 300px;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  margin-bottom: 20px;
}

.panel-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #303133;
}
</style>