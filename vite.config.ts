import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import path from "node:path";

// https://vitejs.dev/config/
export default defineConfig({
    base: './',
    define: {
        'process.env': process.env
    },
    plugins: [
        vue(),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src')
        }
    },
    build: {
        cssCodeSplit: false,
        sourcemap: false,
        rollupOptions: {
            input: {
                login: 'src/pages/login/index.ts',
                frame: 'src/pages/frame/index.ts',
                demo: 'src/pages/demo/index.ts',
                cpmchannel: 'src/pages/cpm/channel/index.ts',
                reportconfig: 'src/pages/reportconfig/index.ts',
                report: 'src/pages/report/index.ts',
                'vtable-test': 'src/pages/vtable-test/index.ts'
            },
            output: {
                entryFileNames: '[name].js',
                assetFileNames: '[name].css',
                chunkFileNames: '[name]-[hash].js',
                manualChunks: {
                    elementPlus: ['element-plus', 'element-plus/es/locale/lang/zh-cn'],
                    vue: ['vue', '@vue/shared', '@vue/reactivity'],
                    vueuse: ['@vueuse/core', '@vueuse/components', '@vueuse/integrations', '@vueuse/shared'],
                    axios: ['axios'],
                    pinia: ['pinia'],
                    fullcalendar: ['@fullcalendar/core', '@fullcalendar/daygrid', '@fullcalendar/interaction', '@fullcalendar/timegrid', '@fullcalendar/vue3'],
                    mdi: ['mdi-vue3']
                },
            },
        },
    },
    server: {
        host: '0.0.0.0',
        proxy: {
            '/api': {
                target: 'http://0.0.0.0:9900',
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, ''),
            },
            '^/(logout|kuainiu|dashboard|cpm|site|assets|css|js|debug|report-capital|dcs|repay|grant|system|event|contract|hotspot|kvmanager|webtask|payment|common-payment|mq|task|cmdb|statement|admin|import|gateway|diy-report|account|union-report-capital|jasypt|audit|dataplatform|alarm|user)': {
                target: 'http://0.0.0.0:9900',
                changeOrigin: true,
            }
        },
    },
})
