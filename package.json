{"name": "adminlte-frame", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@fullcalendar/vue3": "^6.1.11", "@visactor/vtable": "^1.18.5", "@visactor/vue-vtable": "^1.18.5", "@vueuse/components": "^10.9.0", "@vueuse/core": "^10.9.0", "@vueuse/integrations": "^10.9.0", "@vueuse/shared": "^10.9.0", "axios": "^1.6.7", "element-plus": "^2.6.0", "expression-eval": "^5.0.1", "jse-eval": "^1.5.2", "konva": "^9.3.6", "mdi-vue3": "^7.4.47", "monaco-editor": "^0.47.0", "monaco-editor-component": "^0.1.0", "node-sql-parser": "^5.0.0", "pinia": "^2.1.7", "sass": "^1.71.1", "tslib": "^2.8.1", "unplugin-vue-components": "^0.26.0", "vue": "^3.4.21", "vue-hooks-plus": "^2.4.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/node": "^20.11.24", "@vitejs/plugin-vue": "^5.0.4", "prettier": "^3.4.1", "sql-formatter": "^15.4.6", "terser": "^5.29.2", "typescript": "^5.3.3", "vite": "^5.1.4", "vue-tsc": "^1.8.27"}}